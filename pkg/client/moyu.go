package client

import (
	"encoding/json"
	"fmt"
	"github.com/gorilla/websocket"
	"log/slog"
	"net/http"
	"net/url"
	"strings"
	"sync"
	"time"
)

type MoYu struct {
	user          User
	cookie        string
	host          string
	wss           string
	baseUrl       string
	conn          *websocket.Conn
	Opened        bool
	OnEventHandle OnEvent

	// Reconnect configuration
	reconnectEnabled     bool
	reconnectInterval    time.Duration
	maxReconnectAttempts int
	reconnectAttempts    int
	isReconnecting       bool
	connected            bool
	stopReconnect        chan bool
	mutex                sync.RWMutex
	writeMutex           sync.Mutex
}

type OnEvent func(event string, val []any)

func New(baseUrl, cookie string) (*MoYu, error) {
	if baseUrl == "" {
		return nil, fmt.Errorf("host cannot be empty")
	}
	u, err2 := url.Parse(baseUrl)
	if err2 != nil {
		return nil, fmt.Errorf("invalid host: %v", err2)
	}
	var wss = "ws"
	if u.Scheme == "https" {
		wss = "wss"
	}

	c := &MoYu{
		wss:                  wss,
		baseUrl:              baseUrl,
		host:                 u.Host,
		cookie:               cookie,
		reconnectEnabled:     false,
		reconnectInterval:    5 * time.Second,
		maxReconnectAttempts: 10,
		stopReconnect:        make(chan bool, 1),
	}

	session, err := c.getSession(baseUrl, cookie)
	if err != nil {
		return nil, err
	}
	c.user = session.User
	return c, nil
}

func (c *MoYu) Connect() error {
	return c.connect(false)
}

func (c *MoYu) connect(isReconnect bool) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	u := url.URL{
		Scheme:   c.wss,
		Host:     c.host,
		Path:     "/socket.io/",
		RawQuery: "EIO=4&transport=websocket",
	}
	headers := http.Header{
		"Origin":                   []string{c.baseUrl},
		"Cache-Control":            []string{"no-cache"},
		"Accept-Language":          []string{"zh-CN,zh;q=0.9"},
		"Pragma":                   []string{"no-cache"},
		"User-Agent":               []string{"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"},
		"Cookie":                   []string{c.cookie},
		"Sec-WebSocket-Extensions": []string{"permessage-deflate; client_max_window_bits"},
	}

	conn, resp, err := websocket.DefaultDialer.Dial(u.String(), headers)
	if err != nil {
		if resp != nil {
			return fmt.Errorf("HTTP 响应状态: %s", resp.Status)
		}
		return fmt.Errorf("WebSocket 连接失败: %v", err)
	}

	// Close old connection if exists
	if c.conn != nil {
		c.conn.Close()
	}

	c.conn = conn
	c.connected = true
	c.Opened = false

	// Reset reconnect attempts on successful connection
	if !isReconnect {
		c.reconnectAttempts = 0
	}

	go c.readMessages()

	if err := conn.WriteMessage(websocket.TextMessage, []byte("40")); err != nil {
		c.connected = false
		return fmt.Errorf("发送握手消息失败: %v", err)
	}

	if isReconnect {
		slog.Warn("🔄 重连成功",
			c.AttrUserName())
	} else {
		slog.Debug("🔌 连接成功",
			c.AttrUserName())
	}

	return nil
}

func (c *MoYu) readMessages() {
	defer func() {
		c.mutex.Lock()
		c.connected = false
		c.Opened = false
		c.mutex.Unlock()

		// Trigger reconnect if enabled
		if c.reconnectEnabled && !c.isReconnecting {
			go c.handleReconnect()
		}
	}()

	for {
		c.mutex.RLock()
		conn := c.conn
		connected := c.connected
		c.mutex.RUnlock()

		if !connected || conn == nil {
			return
		}

		_, message, err := conn.ReadMessage()
		if err != nil {
			slog.Error("❌ 读取消息错误",
				slog.String("error", err.Error()),
			)
			return
		}
		c.handleSocketIOMessage(string(message), conn)
	}
}

func (c *MoYu) handleSplitIOMessage(message string, conn *websocket.Conn) {
	// 现在服务端的 message 改成了这种, 第一次会发送这个占位符的消息
	//  451-["battle:fullInfo:success",{"_placeholder":true,"num":0}]
	// 接下来才会发送具体的消息体, 并且是加密的 需要使用 DecodeBase64 这个方法进行解码
}

func (c *MoYu) handleSocketIOMessage(message string, conn *websocket.Conn) {
	if len(message) < 1 {
		return
	}

	switch message[0] {
	case '0': // open
		c.Opened = true
	case '1': // close
	case '2': // ping
		conn.WriteMessage(websocket.TextMessage, []byte("3"))
	case '3': // pong
	case '4': // message
		if len(message) > 1 {
			switch message[1] {
			case '0': // connect
				slog.Info("连接成功",
					c.AttrUserName())
				c.mutex.Lock()
				c.reconnectAttempts = 0 // Reset on successful Socket.IO connect
				c.mutex.Unlock()
				if err := c.joinRoom(); err != nil {
					slog.Error("加入房间失败",
						slog.String("error", err.Error()),
						c.AttrUserName())
				}
			case '1': // disconnect
				slog.Warn("房间断开连接",
					c.AttrUserName())
			case '2': // event
				var array []any
				if err := json.Unmarshal([]byte(message[2:]), &array); err != nil {
					slog.Error("解析事件失败",
						slog.String("error", err.Error()),
						slog.String("message", message),
						c.AttrUserName())
					return
				}
				eventAny := array[0]
				event := eventAny.(string)
				slog.Debug("📨 收到事件",
					slog.String("event", event),
					c.AttrUserName(),
				)
				c.OnEvent(event, array[1:])

			case '3': // ack
				slog.Debug("✅ Socket.IO 确认",
					slog.String("message", message[2:]),
					c.AttrUserName())
			}
		}
	}
}

func (c *MoYu) sendMessage(conn *websocket.Conn, event string, data string) error {
	message := fmt.Sprintf(`42["%s",%v]`, event, data)
	return conn.WriteMessage(websocket.TextMessage, []byte(message))
}

func (c *MoYu) joinRoom() error {
	return c.simpleAction("joinRoom")
}

func (c *MoYu) simpleAction(action string) error {
	marshal, err := json.Marshal(&c.user)
	if err != nil {
		return err
	}
	s := fmt.Sprintf(`42["%s",{"user":%s,"data":{}}]`, action, marshal)
	return c.writeMessage(s)
}

func (c *MoYu) action(action string, data any) error {
	marshal, err := json.Marshal(&c.user)
	if err != nil {
		return err
	}
	dataBytes, err := json.Marshal(&data)
	if err != nil {
		return err
	}
	s := fmt.Sprintf(`42["%s",{"user":%s,"data":%s}]`, action, marshal, dataBytes)
	return c.writeMessage(s)
}

func (c *MoYu) writeMessage(message string) error {
	c.writeMutex.Lock()
	defer c.writeMutex.Unlock()
	return c.conn.WriteMessage(websocket.TextMessage, []byte(message))
}

func (c *MoYu) OnEvent(event string, val []any) {
	if c.OnEventHandle != nil {
		c.OnEventHandle(event, val)
	}
}

// Reconnect methods

func (c *MoYu) EnableAutoReconnect(interval time.Duration, maxAttempts int) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.reconnectEnabled = true
	c.reconnectInterval = interval
	c.maxReconnectAttempts = maxAttempts
	slog.Info("🔄 启用自动重连",
		slog.Duration("interval", interval),
		slog.Int("max_attempts", maxAttempts),
	)
}

func (c *MoYu) DisableAutoReconnect() {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.reconnectEnabled = false

	// Stop any ongoing reconnect attempts
	select {
	case c.stopReconnect <- true:
	default:
	}

	slog.Info("🔄 禁用自动重连")
}

func (c *MoYu) IsConnected() bool {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	return c.connected && c.Opened
}

func (c *MoYu) Reconnect() error {
	slog.Info("🔄 手动重连...")
	return c.connect(true)
}

func (c *MoYu) handleReconnect() {
	c.mutex.Lock()
	if c.isReconnecting {
		c.mutex.Unlock()
		return
	}
	c.isReconnecting = true
	c.mutex.Unlock()

	defer func() {
		c.mutex.Lock()
		c.isReconnecting = false
		c.mutex.Unlock()
	}()

	for {
		c.mutex.RLock()
		enabled := c.reconnectEnabled
		attempts := c.reconnectAttempts
		maxAttempts := c.maxReconnectAttempts
		interval := c.reconnectInterval
		c.mutex.RUnlock()

		if !enabled {
			slog.Info("🔄 自动重连已禁用")
			return
		}

		if attempts >= maxAttempts {
			slog.Warn("🔄 达到最大重连次数, 停止重连",
				slog.Int("max_attempts", maxAttempts),
			)
			return
		}

		c.mutex.Lock()
		c.reconnectAttempts++
		currentAttempt := c.reconnectAttempts
		c.mutex.Unlock()

		slog.Info("🔄 尝试重连",
			slog.Int("current_attempt", currentAttempt),
			slog.Int("max_attempts", maxAttempts),
		)

		// Wait before reconnecting (with exponential backoff)
		waitTime := time.Duration(currentAttempt) * interval
		if waitTime > 30*time.Second {
			waitTime = 30 * time.Second
		}

		select {
		case <-c.stopReconnect:
			slog.Info("🔄 重连被停止")
			return
		case <-time.After(waitTime):
		}

		if err := c.connect(true); err != nil {
			slog.Error("🔄 重连失败",
				slog.String("error", err.Error()),
				slog.Int("attempt", currentAttempt),
			)
			continue
		}

		// Wait for connection to be fully established
		for i := 0; i < 50; i++ {
			if c.IsConnected() {
				break
			}
			time.Sleep(100 * time.Millisecond)
		}

		if c.IsConnected() {
			slog.Info("🔄 重连成功，重新加入房间...")
			// Try to rejoin room after successful reconnect
			if err := c.joinRoom(); err != nil {
				slog.Error("🔄 重连后加入房间失败",
					slog.String("error", err.Error()))
			}
			return
		}
	}
}

func (c *MoYu) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	c.reconnectEnabled = false
	c.connected = false
	c.Opened = false

	// Stop reconnect attempts
	select {
	case c.stopReconnect <- true:
	default:
	}

	if c.conn != nil {
		err := c.conn.Close()
		c.conn = nil
		return err
	}

	return nil
}

func (c *MoYu) AttrUserName() slog.Attr {
	return slog.String("user", c.user.Name)
}
