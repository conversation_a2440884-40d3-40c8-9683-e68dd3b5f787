<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Base64解码器</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        .input-section, .output-section {
            margin-bottom: 30px;
        }
        label {
            display: block;
            margin-bottom: 10px;
            font-weight: bold;
            color: #555;
            font-size: 1.1em;
        }
        textarea {
            width: 100%;
            min-height: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            resize: vertical;
            transition: all 0.3s ease;
            box-sizing: border-box;
        }
        textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
        }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px 5px;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .info {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #2196f3;
        }
        .error {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #f44336;
            color: #c62828;
        }
        .success {
            background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 5px solid #4caf50;
            color: #2e7d32;
        }
        .button-group {
            text-align: center;
            margin: 20px 0;
        }
        pre {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            overflow-x: auto;
            border: 1px solid #e9ecef;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-size: 13px;
            line-height: 1.5;
        }
    </style>
</head>
<body>
<div class="container">
    <h1>🔓 Base64解码器</h1>

    <div class="input-section">
        <label for="encodedText">输入Base64编码文本：</label>
        <textarea id="encodedText" placeholder="请粘贴您的Base64编码文本..."></textarea>
    </div>

    <div class="button-group">
        <button onclick="decodeText()">🔓 解码</button>
        <button onclick="clearAll()">🗑️ 清空</button>
        <button onclick="loadSampleData()">📋 加载示例数据</button>
    </div>

    <div id="result"></div>

    <div class="output-section">
        <label for="decodedText">解码结果：</label>
        <textarea id="decodedText" readonly placeholder="解码结果将显示在这里..."></textarea>
    </div>
</div>

<script>
    // 示例数据（从文档中提取的Base64字符串）
    const sampleData = `eJxNj8tOxDAMRf/F60ZKp0nTdocYgZBY8BjYIBZu6hHRdJpRHiA06r/jCATsbN9r+54z5EgBhjPk7CYYQI52s+k7K7QxtVCt1QKbSQnZ9VI2ykjdaahgwSOxe3YHmv07WR65+OyiS56v7XGOVMHehZgeKFK6wxg/fJh+JRevjz/NWsGECUuGg0vp8+k7iOlVMxkigVahUDVK0aM2gkZppRmxn2rDXxPGw32mzGlezoA2Ob/clH3GcjbPKQdiW6ATYbr0eUkw1FLKCmwOgRbOVxQYGlNmgWvauQJXGy3rtled1q2u4A3jlqZs01Wh2vFbJvM5WLamkJlpxpge81gSbf3y78qmbnTbtn1XwYk9RbgYfU63fwtsbDu1vq7rF6iAgZQ=`;

    function loadSampleData() {
        document.getElementById('encodedText').value = sampleData;
        showMessage('示例数据已加载！', 'success');
    }

    function decodeText() {
        const encoded = document.getElementById('encodedText').value.trim();
        const resultDiv = document.getElementById('result');
        const decodedTextarea = document.getElementById('decodedText');

        if (!encoded) {
            showMessage('请输入要解码的Base64文本', 'error');
            return;
        }

        try {
            // 首先进行Base64解码
            const binaryString = atob(encoded);

            // 将二进制字符串转换为Uint8Array
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            // 检查是否是压缩数据（通常以特定字节开头）
            if (bytes.length > 2 && bytes[0] === 0x78) {
                // 可能是zlib压缩数据
                try {
                    // 使用pako库进行解压缩
                    if (typeof pako !== 'undefined') {
                        const decompressed = pako.inflate(bytes, { to: 'string' });
                        decodedTextarea.value = decompressed;
                        showMessage('Base64解码并解压缩成功！数据可能经过了zlib压缩。', 'success');
                    } else {
                        // 如果没有pako库，尝试直接显示为文本
                        const text = new TextDecoder('utf-8', { fatal: false }).decode(bytes);
                        decodedTextarea.value = text;
                        showMessage('Base64解码成功！注意：数据可能经过了压缩，如需完整解压请使用专门的解压工具。', 'info');
                    }
                } catch (e) {
                    // 如果解压失败，尝试作为普通文本显示
                    const text = new TextDecoder('utf-8', { fatal: false }).decode(bytes);
                    decodedTextarea.value = text;
                    showMessage('Base64解码成功，但数据可能经过了压缩或为二进制格式。', 'info');
                }
            } else {
                // 尝试作为UTF-8文本解码
                try {
                    const text = new TextDecoder('utf-8', { fatal: true }).decode(bytes);
                    decodedTextarea.value = text;
                    showMessage('Base64解码成功！', 'success');
                } catch (e) {
                    // UTF-8解码失败，可能是二进制数据
                    const text = new TextDecoder('utf-8', { fatal: false }).decode(bytes);
                    decodedTextarea.value = text;
                    showMessage('Base64解码成功，但内容可能包含二进制数据或使用了其他编码格式。', 'info');
                }
            }

            // 显示一些统计信息
            const stats = `
                    <div class="info">
                        <strong>解码统计：</strong><br>
                        原始Base64长度: ${encoded.length} 字符<br>
                        解码后数据长度: ${bytes.length} 字节<br>
                        解码后文本长度: ${decodedTextarea.value.length} 字符
                    </div>
                `;
            resultDiv.innerHTML = stats;

        } catch (error) {
            showMessage(`解码失败：${error.message}`, 'error');
            console.error('解码错误:', error);
        }
    }

    function showMessage(message, type) {
        const resultDiv = document.getElementById('result');
        const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
        resultDiv.innerHTML = `<div class="${className}">${message}</div>`;
    }

    function clearAll() {
        document.getElementById('encodedText').value = '';
        document.getElementById('decodedText').value = '';
        document.getElementById('result').innerHTML = '';
    }

    // 自动加载示例数据
    window.onload = function() {
        loadSampleData();
        // 尝试自动解码
        setTimeout(decodeText, 500);
    };
</script>

<!-- 引入pako库用于解压缩 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
</body>
</html>