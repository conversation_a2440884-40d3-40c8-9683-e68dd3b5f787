package main

import (
	"encoding/json"
	"log/slog"
	"moyu/pkg/client"
	"time"
)

func main() {
	// Setup structured logging
	var cookie = "Hm_lvt_987895b377fdb91c7d28c2621b2c6b86=**********; HMACCOUNT=96ED20F1C572F91D; nuxt-session=Fe26.2**3eba568f24e0ceb7d3e1a9cb19a3b5f940501f6f3f311aea9efa0951e19710da*8J8P_QTjpBIyUGrGgz0jYw*-wc5P8qc7vUr31BVYZlDAiPg9UOpaeOWbusU2jliSQFknAmWPyiQuEEFkOa8yQNqtdCh-P8q-2QOGSKPlp9rfiJVZ7s-Qhl8dorY5G9nezPZ_86ysQmZ_29N0xhe1_YxmJap5o9uckPQs76JSiEHQ4P2WT7UabS4tYACGSLVH6Qy19Qgjcv3sYG-MS_84RA9ncTc8Fe6Zb5EpZY75uO33VDCXLpR9LspZiwmzxguAxpMsi7X1sLXszDLAvY4WWxG-LpT43O3WDitby9R7JecwSmpfAY-sQRMIgbDH-Ynnds***************c92adb2ddb634c9b2fa4b053bd531185760688e5c5a8a971901ede6fbd847de8*yY-sBQcIa2bBFFCMhhfJ-x4ZFSIV98Wgbc-X49ckEtw; Hm_lpvt_987895b377fdb91c7d28c2621b2c6b86=**********"
	c, err := client.New("https://www.moyu-idle.com", cookie)
	if err != nil {
		slog.Error("创建客户端失败", slog.String("error", err.Error()))
		return
	}

	// Enable auto-reconnect with 5 second interval and max 10 attempts
	c.EnableAutoReconnect(5*time.Second, 10000)

	if err := c.Connect(); err != nil {
		slog.Error("连接失败", slog.String("error", err.Error()))
		return
	}

	go func() {
		for {
			time.Sleep(100 * time.Millisecond)
			if c.Opened {
				break
			}
		}
		var room client.BattleRoom
		var startCount int
		var startBattle = func(room client.BattleRoom) {
			if room.IsInBattleRoom == false {
				return
			}
			time.Sleep(200 * time.Millisecond)
			if err := c.BattleRoomUpdateReadyState(room.UUID, true); err != nil {
				slog.Error("发送更新准备状态失败", slog.String("error", err.Error()), c.AttrUserName(), slog.String("room_id", room.UUID))
				return
			}

			if room.CheckAllReady() == false {
				slog.Info("有用户未准备,不开始战斗")
				return
			}

			if room.Status == "battling" || room.Status == "" {
				return
			}

			time.Sleep(200 * time.Millisecond)
			if err := c.BattleRoomStart(room.UUID); err != nil {
				slog.Error("发送开始战斗失败", slog.String("error", err.Error()), c.AttrUserName(), slog.String("room_id", room.UUID))
				return
			}
			slog.Info("开始战斗", slog.String("room_id", room.UUID), c.AttrUserName())
		}

		if err := c.BattleRoomGetCurrentRoom(); err != nil {
			slog.Error("发送获取当前房间失败", slog.String("error", err.Error()), c.AttrUserName())
		}

		go func() {
			for {
				time.Sleep(3 * time.Second)
				if err := c.BattleRoomGetCurrentRoom(); err != nil {
					slog.Error("发送获取当前房间失败", slog.String("error", err.Error()), c.AttrUserName())
					continue
				}
				startBattle(room)
			}
		}()

		c.OnEventHandle = func(event string, val []any) {
			if event == "battle:leaveBattle:success" {
				room.IsInBattleRoom = false
			}
			if event == "battleRoom:join:success" || event == "battleRoom:update" || event == "battleRoom:getCurrentRoom:success" {
				data := val[0].(map[string]any)["data"]
				var successResult client.BattleRoomUpdate
				if err := cast(data, &successResult); err != nil {
					slog.Error("解析事件失败",
						slog.String("error", err.Error()),
						slog.String("event", event),
						c.AttrUserName(),
					)
					return
				}
				if successResult.Uuid != "" {
					room.IsInBattleRoom = true
				}
				room.UUID = successResult.Uuid
				if successResult.BattleStatus != "" {
					room.Status = successResult.BattleStatus
				} else {
					room.Status = successResult.Status
				}
				room.MemberIDs = successResult.MemberIds
				room.ReadyMap = successResult.ReadyMap
				room.CurrentRepeat = successResult.CurrentRepeat
				room.RepeatCount = successResult.RepeatCount

			}
			if event == "battle:end:success" {
				startCount++
				slog.Info("战斗结束次数", slog.Int("count", startCount))
				go startBattle(room)
			}
		}

		//if err := c.BattleRoomCreate(&BattleRoomCreateRequest{
		//	Area:        AREA_悠闲平原,
		//	IsPublic:    true,
		//	Name:        "111111",
		//	RepeatCount: 1,
		//}); err != nil {
		//	panic(err)
		//}
	}()
	select {}
}

func cast[T any](source any, target *T) error {
	bytes, err := json.Marshal(source)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, target)
}
