package client

import "testing"

func TestDecoder(t *testing.T) {
	var msg = "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"
	base64, err := DecodeBase64(msg)
	if err != nil {
		t.Fatal(err)
	}
	base64.Print()
}
