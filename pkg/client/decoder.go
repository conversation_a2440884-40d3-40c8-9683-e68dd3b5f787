package client

import (
	"bytes"
	"compress/zlib"
	"encoding/base64"
	"fmt"
	"io"
	"strings"
	"unicode/utf8"
)

type Base64Decoder struct {
	data string
}

func DecodeBase64(data string) (*DecodeResult, error) {
	return NewBase64Decoder(data).Decode()
}

func NewBase64Decoder(data string) *Base64Decoder {
	return &Base64Decoder{data: strings.TrimSpace(data)}
}

func (d *Base64Decoder) Decode() (*DecodeResult, error) {
	if d.data == "" {
		return nil, fmt.Errorf("输入数据为空")
	}

	// Base64解码
	decodedBytes, err := base64.StdEncoding.DecodeString(d.data)
	if err != nil {
		return nil, fmt.Errorf("Base64解码失败: %v", err)
	}

	result := &DecodeResult{
		OriginalLength: len(d.data),
		DecodedLength:  len(decodedBytes),
		RawBytes:       decodedBytes,
	}

	// 检查是否为压缩数据
	if d.isCompressedData(decodedBytes) {
		// 尝试解压缩
		decompressedText, err := d.decompressZlib(decodedBytes)
		if err != nil {
			// 解压失败，尝试作为普通文本处理
			result.Text = d.bytesToText(decodedBytes)
			result.Type = "binary_or_encoded"
			result.Message = fmt.Sprintf("Base64解码成功，但解压缩失败: %v", err)
		} else {
			result.Text = decompressedText
			result.Type = "compressed_text"
			result.Message = "Base64解码并解压缩成功！数据经过了zlib压缩。"
		}
	} else {
		// 普通数据，直接转换为文本
		result.Text = d.bytesToText(decodedBytes)
		if utf8.ValidString(result.Text) {
			result.Type = "plain_text"
			result.Message = "Base64解码成功！"
		} else {
			result.Type = "binary_data"
			result.Message = "Base64解码成功，但内容可能包含二进制数据。"
		}
	}

	result.TextLength = len(result.Text)
	return result, nil
}

func (d *Base64Decoder) isCompressedData(data []byte) bool {
	if len(data) < 2 {
		return false
	}
	// zlib数据通常以0x78开头
	return data[0] == 0x78 && (data[1] == 0x01 || data[1] == 0x9C || data[1] == 0xDA)
}

func (d *Base64Decoder) decompressZlib(data []byte) (string, error) {
	reader := bytes.NewReader(data)
	zlibReader, err := zlib.NewReader(reader)
	if err != nil {
		return "", err
	}
	defer zlibReader.Close()

	var buf bytes.Buffer
	_, err = io.Copy(&buf, zlibReader)
	if err != nil {
		return "", err
	}

	return buf.String(), nil
}

func (d *Base64Decoder) bytesToText(data []byte) string {
	return string(data)
}

type DecodeResult struct {
	OriginalLength int    // 原始Base64长度
	DecodedLength  int    // 解码后字节长度
	TextLength     int    // 文本长度
	Text           string // 解码后的文本
	Type           string // 数据类型
	Message        string // 处理消息
	RawBytes       []byte // 原始字节数据
}

func (r *DecodeResult) Print() {
	fmt.Println("=== Base64解码结果 ===")
	fmt.Printf("消息: %s\n", r.Message)
	fmt.Printf("数据类型: %s\n", r.Type)
	fmt.Printf("原始Base64长度: %d 字符\n", r.OriginalLength)
	fmt.Printf("解码后数据长度: %d 字节\n", r.DecodedLength)
	fmt.Printf("解码后文本长度: %d 字符\n", r.TextLength)
	fmt.Println("===================")
	fmt.Println("解码内容:")
	fmt.Println(r.Text)
}
